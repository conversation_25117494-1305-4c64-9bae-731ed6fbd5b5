import streamlit as st
import pandas as pd
import numpy as np
import json


# 配置管理：加载和保存配置文件
def config(rows=None, columns=None):
    try:
        # 加载配置
        try:
            with open("config.json", "r") as f:
                cfg = json.load(f)
        except:
            cfg = {"rows": 10, "columns": 5}

        # 保存新配置
        if rows or columns:
            cfg.update(
                {"rows": rows or cfg["rows"], "columns": columns or cfg["columns"]}
            )
            with open("config.json", "w") as f:
                json.dump(cfg, f, indent=4)
        return cfg
    except:
        return {"rows": 10, "columns": 5}


def save_config():
    """配置更新函数"""
    new_rows = st.session_state.get("rows_input", st.session_state.rows)
    new_columns = st.session_state.get("columns_input", st.session_state.columns)
    config(new_rows, new_columns)
    st.session_state.rows = new_rows
    st.session_state.columns = new_columns


def main():
    """主函数"""
    # 页面设置
    st.set_page_config(page_title="随机数据表格生成器", page_icon="📊", layout="wide")

    # 初始化 session_state
    cfg = config()
    if "rows" not in st.session_state:
        st.session_state.rows = cfg["rows"]
        st.session_state.columns = cfg["columns"]

    # 侧边栏配置
    st.sidebar.header("⚙️ 表格设置")
    rows = st.sidebar.number_input("行数", 1, 1000, st.session_state.rows, key="rows_input", on_change=save_config)
    columns = st.sidebar.number_input("列数", 1, 50, st.session_state.columns, key="columns_input", on_change=save_config)

    # 确保 session_state 与当前值同步
    st.session_state.rows, st.session_state.columns = rows, columns
    st.sidebar.info(f"当前: {rows} × {columns}")

    if st.button("🔄 重新生成", type="primary"):
        st.rerun()

    # 生成并显示数据
    data = np.random.randn(rows, columns)
    df = pd.DataFrame(data, columns=[f"列_{i + 1}" for i in range(columns)])
    st.dataframe(df, use_container_width=True, height=min(600, rows * 35 + 100))


if __name__ == "__main__":
    main()
