import streamlit as st
import pandas as pd
import numpy as np
import json
import os


def get_config_path():
    """获取配置文件的完整路径"""
    # 从环境变量获取配置目录，如果没有则使用当前目录
    config_dir = os.environ.get('APP_CONFIG_DIR', '.')
    return os.path.join(config_dir, "config.json")


# 配置管理：加载和保存配置文件
def config(rows=None, columns=None):
    try:
        config_path = get_config_path()

        # 加载配置
        try:
            with open(config_path, "r", encoding='utf-8') as f:
                cfg = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError, OSError):
            cfg = {"rows": 10, "columns": 5}

        # 保存新配置
        if rows or columns:
            cfg.update(
                {"rows": rows or cfg["rows"], "columns": columns or cfg["columns"]}
            )
            # 确保目录存在
            config_dir = os.path.dirname(config_path)
            if config_dir:  # 只有当目录不为空时才创建
                os.makedirs(config_dir, exist_ok=True)
            with open(config_path, "w", encoding='utf-8') as f:
                json.dump(cfg, f, indent=4, ensure_ascii=False)
        return cfg
    except Exception as e:
        # 在开发环境中可以打印错误信息（通过环境变量判断）
        if not os.environ.get('APP_CONFIG_DIR'):
            print(f"配置文件操作错误: {e}")
        return {"rows": 10, "columns": 5}


def save_config():
    """配置更新函数"""
    new_rows = st.session_state.get("rows_input", st.session_state.rows)
    new_columns = st.session_state.get("columns_input", st.session_state.columns)
    config(new_rows, new_columns)
    st.session_state.rows = new_rows
    st.session_state.columns = new_columns


def main():
    """主函数"""
    # 页面设置
    st.set_page_config(page_title="随机数据表格生成器", page_icon="📊", layout="wide")

    # 初始化 session_state
    cfg = config()
    if "rows" not in st.session_state:
        st.session_state.rows = cfg["rows"]
        st.session_state.columns = cfg["columns"]

    # 侧边栏配置
    st.sidebar.header("⚙️ 表格设置")
    rows = st.sidebar.number_input("行数", 1, 1000, st.session_state.rows, key="rows_input", on_change=save_config)
    columns = st.sidebar.number_input("列数", 1, 50, st.session_state.columns, key="columns_input", on_change=save_config)

    # 确保 session_state 与当前值同步
    st.session_state.rows, st.session_state.columns = rows, columns
    st.sidebar.info(f"当前: {rows} × {columns}")

    if st.button("🔄 重新生成", type="primary"):
        st.rerun()

    # 生成并显示数据
    data = np.random.randn(rows, columns)
    df = pd.DataFrame(data, columns=[f"列_{i + 1}" for i in range(columns)])
    st.dataframe(df, use_container_width=True, height=min(600, rows * 35 + 100))


if __name__ == "__main__":
    main()
