# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files, copy_metadata


a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('main.py', '.'),
        ('config.json', '.'),
    ] + collect_data_files('streamlit') + copy_metadata('streamlit'),
    hiddenimports=[
        'streamlit.web.cli',
        'streamlit.runtime.scriptrunner.magic_funcs',
    ],
    hookspath=['./hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='run',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
