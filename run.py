import sys
import os
import streamlit.web.cli as stcli



def resolve_path(path):
    """
    解析文件路径，确保在不同环境下都能正确定位文件
    - 打包环境：相对于可执行文件所在目录
    - 开发环境：相对于脚本所在目录
    """
    if getattr(sys, 'frozen', False):
        # 在打包环境中，使用可执行文件所在目录
        base_path = os.path.dirname(sys.executable)
    else:
        # 在开发环境中，使用脚本所在目录
        base_path = os.path.dirname(os.path.abspath(__file__))

    resolved_path = os.path.abspath(os.path.join(base_path, path))
    return resolved_path


if __name__ == "__main__":
    sys.argv = [
        "streamlit",
        "run",
        resolve_path("main.py"),
        "--global.developmentMode=false",
    ]
    sys.exit(stcli.main())