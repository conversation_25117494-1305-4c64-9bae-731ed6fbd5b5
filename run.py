import sys
import os
import streamlit.web.cli as stcli


def get_app_directory():
    """
    获取应用程序所在目录
    - 打包环境：可执行文件所在目录
    - 开发环境：run.py 脚本所在目录
    """
    if getattr(sys, 'frozen', False):
        # 在打包环境中，使用可执行文件所在目录
        return os.path.dirname(sys.executable)
    else:
        # 在开发环境中，使用 run.py 脚本所在目录
        return os.path.dirname(os.path.abspath(__file__))


def resolve_path(path):
    """解析相对于应用程序目录的文件路径"""
    base_path = get_app_directory()
    resolved_path = os.path.abspath(os.path.join(base_path, path))
    return resolved_path


if __name__ == "__main__":
    # 设置配置文件目录环境变量，供 main.py 使用
    app_dir = get_app_directory()
    os.environ['APP_CONFIG_DIR'] = app_dir

    sys.argv = [
        "streamlit",
        "run",
        resolve_path("main.py"),
        "--global.developmentMode=false",
    ]
    sys.exit(stcli.main())